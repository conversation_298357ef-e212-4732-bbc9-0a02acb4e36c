// Simple stats tracker without complex class structure
const statsTracker = {
  // Current wave stats
  currentWave: {
    waveNumber: 0,
    startTime: 0,
    endTime: 0,
    enemiesKilled: 0,
    damageDealt: 0,
    damageTaken: 0,
    totalMovementDistance: 0,
    timeInDanger: 0,
    averageDistanceToEnemies: 0,
    maxEnemiesAlive: 0,
    playerPositions: [],
    enemyEncounters: 0
  },

  // Tracking variables
  lastPlayerPosition: { x: 0, y: 0 },
  dangerStartTime: 0,
  isInDanger: false,
  enemyDistanceSamples: [],
  currentEnemiesAlive: 0,

  reset() {
    this.currentWave = {
      waveNumber: 0,
      startTime: 0,
      endTime: 0,
      enemiesKilled: 0,
      damageDealt: 0,
      damageTaken: 0,
      totalMovementDistance: 0,
      timeInDanger: 0,
      averageDistanceToEnemies: 0,
      maxEnemiesAlive: 0,
      playerPositions: [],
      enemyEncounters: 0
    };
    this.lastPlayerPosition = { x: 0, y: 0 };
    this.dangerStartTime = 0;
    this.isInDanger = false;
    this.enemyDistanceSamples = [];
    this.currentEnemiesAlive = 0;
  },

  startNewWave(waveNumber) {
    this.currentWave.waveNumber = waveNumber;
    this.currentWave.startTime = Date.now();
    this.currentWave.playerPositions = [];
    this.enemyDistanceSamples = [];
    this.currentEnemiesAlive = 0;
    this.currentWave.maxEnemiesAlive = 0;

    console.log(`Starting Wave ${waveNumber}`);
  },

  onEnemySpawned() {
    this.currentEnemiesAlive++;
    this.currentWave.maxEnemiesAlive = Math.max(this.currentWave.maxEnemiesAlive, this.currentEnemiesAlive);
  },

  onEnemyKilled(damageDealt = 1) {
    this.currentWave.enemiesKilled++;
    this.currentWave.damageDealt += damageDealt;
    this.currentEnemiesAlive = Math.max(0, this.currentEnemiesAlive - 1);
  },

  onPlayerTakesDamage(damage) {
    this.currentWave.damageTaken += damage;
  },

  updatePlayerPosition(x, y) {
    // Track movement distance
    if (this.lastPlayerPosition.x !== 0 || this.lastPlayerPosition.y !== 0) {
      const distance = Math.sqrt(
        Math.pow(x - this.lastPlayerPosition.x, 2) +
        Math.pow(y - this.lastPlayerPosition.y, 2)
      );
      this.currentWave.totalMovementDistance += distance;
    }

    this.lastPlayerPosition = { x, y };
    this.currentWave.playerPositions.push({ x, y, timestamp: Date.now() });

    // Keep only last 100 positions to avoid memory issues
    if (this.currentWave.playerPositions.length > 100) {
      this.currentWave.playerPositions.shift();
    }
  },

  updateEnemyProximity(enemies, playerX, playerY) {
    if (!enemies || enemies.length === 0) {
      this.setDangerStatus(false);
      return;
    }

    // Calculate distances to all enemies
    const distances = enemies.map(enemy => {
      return Math.sqrt(
        Math.pow(enemy.x - playerX, 2) +
        Math.pow(enemy.y - playerY, 2)
      );
    });

    const minDistance = Math.min(...distances);
    const avgDistance = distances.reduce((a, b) => a + b, 0) / distances.length;

    this.enemyDistanceSamples.push(avgDistance);

    // Consider "in danger" if closest enemy is within 100 pixels
    const dangerThreshold = 100;
    this.setDangerStatus(minDistance < dangerThreshold);

    // Track enemy encounters (when enemies get very close)
    if (minDistance < 50) {
      this.currentWave.enemyEncounters++;
    }
  },

  setDangerStatus(inDanger) {
    const currentTime = Date.now();

    if (inDanger && !this.isInDanger) {
      // Entering danger
      this.isInDanger = true;
      this.dangerStartTime = currentTime;
    } else if (!inDanger && this.isInDanger) {
      // Leaving danger
      this.isInDanger = false;
      this.currentWave.timeInDanger += currentTime - this.dangerStartTime;
    }
  },

  finalizeWaveStats() {
    this.currentWave.endTime = Date.now();

    // Finalize danger time if still in danger
    if (this.isInDanger) {
      this.currentWave.timeInDanger += this.currentWave.endTime - this.dangerStartTime;
      this.isInDanger = false;
    }

    // Calculate average distance to enemies
    if (this.enemyDistanceSamples.length > 0) {
      this.currentWave.averageDistanceToEnemies =
        this.enemyDistanceSamples.reduce((a, b) => a + b, 0) / this.enemyDistanceSamples.length;
    }

    // Calculate derived metrics
    const waveDuration = this.currentWave.endTime - this.currentWave.startTime;
    const finalStats = {
      ...this.currentWave,
      waveDuration,
      killsPerSecond: this.currentWave.enemiesKilled / (waveDuration / 1000),
      damagePerSecond: this.currentWave.damageDealt / (waveDuration / 1000),
      movementPerSecond: this.currentWave.totalMovementDistance / (waveDuration / 1000),
      dangerTimeRatio: this.currentWave.timeInDanger / waveDuration,
      survivalEfficiency: this.currentWave.enemiesKilled / Math.max(1, this.currentWave.damageTaken)
    };

    console.log('Wave Stats:', finalStats);
    return finalStats;
  },

  getCurrentStats() {
    return { ...this.currentWave };
  }
};

// Export the object directly
export { statsTracker };
