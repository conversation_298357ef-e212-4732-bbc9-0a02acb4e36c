import Phaser from 'phaser';

export default class Enemy extends Phaser.Physics.Arcade.Sprite {
  constructor(scene, x, y) {
    super(scene, x, y, 'enemy');
    
    // Add to scene and enable physics
    scene.add.existing(this);
    scene.physics.add.existing(this);
    
    // Enemy properties
    this.health = 30;
    this.maxHealth = 30;
    this.speed = 80;
    this.damage = 10;
    this.lastDamageTime = 0;
    this.damageInterval = 1000; // 1 second between damage instances
    
    // Set up physics body
    this.setCollideWorldBounds(true);
    this.setBounce(0.2);
    
    // Make enemy slightly red to distinguish from player
    this.setTint(0xff6666);
    
    // Create health bar above enemy
    this.createHealthBar();
  }

  createHealthBar() {
    // Create a simple health bar
    this.healthBarBg = this.scene.add.rectangle(this.x, this.y - 20, 30, 4, 0x000000);
    this.healthBar = this.scene.add.rectangle(this.x, this.y - 20, 30, 4, 0x00ff00);
  }

  update() {
    // Chase the player
    if (this.scene.player && this.scene.player.active) {
      this.scene.physics.moveToObject(this, this.scene.player, this.speed);
    }
    
    // Update health bar position
    if (this.healthBarBg && this.healthBar) {
      this.healthBarBg.x = this.x;
      this.healthBarBg.y = this.y - 20;
      this.healthBar.x = this.x;
      this.healthBar.y = this.y - 20;
      
      // Update health bar width based on current health
      const healthPercent = this.health / this.maxHealth;
      this.healthBar.width = 30 * healthPercent;
      
      // Change color based on health
      if (healthPercent > 0.6) {
        this.healthBar.fillColor = 0x00ff00; // Green
      } else if (healthPercent > 0.3) {
        this.healthBar.fillColor = 0xffff00; // Yellow
      } else {
        this.healthBar.fillColor = 0xff0000; // Red
      }
    }
  }

  takeDamage(amount) {
    this.health -= amount;
    this.health = Math.max(0, this.health);
    
    // Flash white when taking damage
    this.setTint(0xffffff);
    this.scene.time.delayedCall(100, () => {
      this.setTint(0xff6666);
    });
    
    // Check if enemy is dead
    if (this.health <= 0) {
      this.die();
    }
  }

  die() {
    // Remove health bar
    if (this.healthBarBg) {
      this.healthBarBg.destroy();
    }
    if (this.healthBar) {
      this.healthBar.destroy();
    }
    
    // Destroy the enemy
    this.destroy();
  }

  canDamagePlayer() {
    const currentTime = this.scene.time.now;
    if (currentTime - this.lastDamageTime > this.damageInterval) {
      this.lastDamageTime = currentTime;
      return true;
    }
    return false;
  }

  destroy() {
    // Clean up health bar
    if (this.healthBarBg) {
      this.healthBarBg.destroy();
    }
    if (this.healthBar) {
      this.healthBar.destroy();
    }
    super.destroy();
  }
}
