import Phaser from 'phaser';

export default class Player extends Phaser.Physics.Arcade.Sprite {
  constructor(scene, x, y) {
    super(scene, x, y, 'player');
    
    // Add to scene and enable physics
    scene.add.existing(this);
    scene.physics.add.existing(this);
    
    // Player properties
    this.health = 100;
    this.maxHealth = 100;
    this.speed = 200;
    
    // Set up physics body
    this.setCollideWorldBounds(true);
    this.setDrag(300); // Add some drag for smoother movement
    
    // Create health display
    this.healthText = scene.add.text(10, 40, `Health: ${this.health}`, {
      fontSize: '16px',
      fill: '#ffffff'
    });
    this.healthText.setScrollFactor(0); // Keep UI fixed to camera
  }

  update(cursors) {
    // Reset velocity
    this.setVelocity(0);

    // Handle movement input
    if (cursors.left.isDown) {
      this.setVelocityX(-this.speed);
    } else if (cursors.right.isDown) {
      this.setVelocityX(this.speed);
    }

    if (cursors.up.isDown) {
      this.setVelocityY(-this.speed);
    } else if (cursors.down.isDown) {
      this.setVelocityY(this.speed);
    }

    // Normalize diagonal movement
    if (this.body.velocity.x !== 0 && this.body.velocity.y !== 0) {
      this.setVelocity(this.body.velocity.x * 0.707, this.body.velocity.y * 0.707);
    }
  }

  takeDamage(amount) {
    this.health -= amount;
    this.health = Math.max(0, this.health);
    
    // Update health display
    this.healthText.setText(`Health: ${this.health}`);
    
    // Flash red when taking damage
    this.setTint(0xff0000);
    this.scene.time.delayedCall(100, () => {
      this.clearTint();
    });
    
    // Check if player is dead
    if (this.health <= 0) {
      this.die();
    }
  }

  die() {
    // Game over - restart the scene
    this.scene.add.text(400, 300, 'GAME OVER', {
      fontSize: '32px',
      fill: '#ff0000'
    }).setOrigin(0.5);
    
    this.scene.time.delayedCall(2000, () => {
      this.scene.scene.restart();
    });
  }

  destroy() {
    if (this.healthText) {
      this.healthText.destroy();
    }
    super.destroy();
  }
}
