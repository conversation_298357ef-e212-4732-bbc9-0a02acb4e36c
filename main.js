import Phaser from 'phaser';
import Player from './src/Player.js';
import Enemy from './src/Enemy.js';

// Main game scene
class GameScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameScene' });
  }

  preload() {
    // Create simple colored rectangles as sprites
    this.createPlayerSprite();
    this.createEnemySprite();
  }

  createPlayerSprite() {
    // Create a blue rectangle for the player
    const graphics = this.add.graphics();
    graphics.fillStyle(0x0066ff);
    graphics.fillRect(0, 0, 32, 32);
    graphics.generateTexture('player', 32, 32);
    graphics.destroy();
  }

  createEnemySprite() {
    // Create a red rectangle for enemies
    const graphics = this.add.graphics();
    graphics.fillStyle(0xff3333);
    graphics.fillRect(0, 0, 24, 24);
    graphics.generateTexture('enemy', 24, 24);
    graphics.destroy();
  }

  create() {
    // Create player
    this.player = new Player(this, 400, 300);

    // Set up keyboard controls
    this.cursors = this.input.keyboard.createCursorKeys();

    // Create enemy group
    this.enemies = this.physics.add.group({
      classType: Enemy,
      runChildUpdate: true // This ensures enemy update methods are called
    });

    // Create garlic aura (invisible zone around player for combat)
    this.garlicAura = this.add.zone(this.player.x, this.player.y, 80, 80);
    this.physics.add.existing(this.garlicAura);
    this.garlicAura.body.setCircle(40);

    // Set up collisions and overlaps
    this.setupCollisions();

    // Start enemy spawning
    this.startEnemySpawning();

    // UI
    this.add.text(10, 10, 'Use arrow keys to move', { fontSize: '16px', fill: '#ffffff' });
    this.add.text(10, 70, 'Enemies take damage in your aura', { fontSize: '14px', fill: '#ffff00' });
  }

  setupCollisions() {
    // Player-Enemy collision (enemies damage player)
    this.physics.add.overlap(this.player, this.enemies, this.handlePlayerEnemyCollision, null, this);

    // Garlic aura-Enemy overlap (aura damages enemies)
    this.physics.add.overlap(this.garlicAura, this.enemies, this.handleGarlicEnemyOverlap, null, this);
  }

  startEnemySpawning() {
    // Spawn an enemy every 1.5 seconds
    this.time.addEvent({
      delay: 1500,
      callback: this.spawnEnemy,
      callbackScope: this,
      loop: true
    });
  }

  spawnEnemy() {
    // Choose a random edge of the screen to spawn from
    const side = Phaser.Math.Between(0, 3);
    let x, y;

    switch (side) {
      case 0: // Top
        x = Phaser.Math.Between(0, 800);
        y = -50;
        break;
      case 1: // Right
        x = 850;
        y = Phaser.Math.Between(0, 600);
        break;
      case 2: // Bottom
        x = Phaser.Math.Between(0, 800);
        y = 650;
        break;
      case 3: // Left
        x = -50;
        y = Phaser.Math.Between(0, 600);
        break;
    }

    // Create enemy and add to group
    const enemy = new Enemy(this, x, y);
    this.enemies.add(enemy);
  }

  handlePlayerEnemyCollision(player, enemy) {
    // Enemy damages player if enough time has passed
    if (enemy.canDamagePlayer()) {
      player.takeDamage(enemy.damage);
    }
  }

  handleGarlicEnemyOverlap(aura, enemy) {
    // Garlic aura damages enemies over time
    enemy.takeDamage(1); // Small damage per frame while in aura
  }

  update() {
    // Update player
    if (this.player && this.player.active) {
      this.player.update(this.cursors);

      // Update garlic aura position to follow player
      this.garlicAura.x = this.player.x;
      this.garlicAura.y = this.player.y;
    }
  }
}

// Phaser game configuration
const config = {
  type: Phaser.AUTO, // Automatically use WebGL or Canvas
  width: 800,
  height: 600,
  parent: 'game-container',
  physics: {
    default: 'arcade',
    arcade: {
      gravity: { y: 0 },
    }
  },
  scene: [GameScene]
};

// Create the game instance
const game = new Phaser.Game(config);
