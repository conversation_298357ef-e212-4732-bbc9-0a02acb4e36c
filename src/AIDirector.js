// Temporarily simplified version without TensorFlow for testing
class AIDirector {
  constructor() {
    this.isInitialized = false;
    this.init();
  }

  async init() {
    try {
      console.log('Initializing AI Director (Simple Mode)...');

      // Simple rule-based classifier for now
      this.isInitialized = true;
      console.log('AI Director initialized successfully!');
    } catch (error) {
      console.error('Failed to initialize AI Director:', error);
    }
  }

  // Simple rule-based style classification
  classifyStyle(features) {
    const [killsPerSecond, movementPerSecond, dangerTimeRatio, survivalEfficiency, averageDistance, totalKills, maxEnemies] = features;

    // Simple thresholds for classification
    const isHighMovement = movementPerSecond > 0.6;
    const isHighKills = killsPerSecond > 0.5;
    const isHighDanger = dangerTimeRatio > 0.5;
    const isCloseToEnemies = averageDistance < 0.5;

    // Aggressive: High movement, high kills, close to enemies
    if (isHighMovement && isHighKills && isCloseToEnemies) {
      return { label: 'aggressive', confidence: 0.8 };
    }

    // Defensive: Low movement, low danger time, far from enemies
    if (!isHighMovement && !isHighDanger && !isCloseToEnemies) {
      return { label: 'defensive', confidence: 0.8 };
    }

    // Default to balanced
    return { label: 'balanced', confidence: 0.6 };
  }

  normalizeStats(stats) {
    // Normalize stats to 0-1 range for better ML performance
    const normalized = {
      killsPerSecond: Math.min(stats.killsPerSecond / 2, 1), // Normalize assuming max 2 kills/sec
      movementPerSecond: Math.min(stats.movementPerSecond / 200, 1), // Normalize assuming max 200 pixels/sec
      dangerTimeRatio: Math.min(stats.dangerTimeRatio, 1),
      survivalEfficiency: Math.min(stats.survivalEfficiency / 10, 1), // Normalize assuming max 10:1 ratio
      averageDistanceToEnemies: Math.min(stats.averageDistanceToEnemies / 300, 1), // Normalize assuming max 300 pixels
      enemiesKilled: Math.min(stats.enemiesKilled / 20, 1), // Normalize assuming max 20 kills per wave
      maxEnemiesAlive: Math.min(stats.maxEnemiesAlive / 10, 1) // Normalize assuming max 10 enemies
    };

    return normalized;
  }

  createFeatureVector(stats) {
    const normalized = this.normalizeStats(stats);
    
    // Create feature vector: [kills/sec, movement/sec, danger_ratio, survival_eff, avg_distance, total_kills, max_enemies]
    return [
      normalized.killsPerSecond,
      normalized.movementPerSecond,
      normalized.dangerTimeRatio,
      normalized.survivalEfficiency,
      normalized.averageDistanceToEnemies,
      normalized.enemiesKilled,
      normalized.maxEnemiesAlive
    ];
  }

  async predictPlayerStyle(finalStats) {
    if (!this.isInitialized) {
      console.warn('AI Director not initialized yet');
      return null;
    }

    try {
      // Create feature vector from stats
      const features = this.createFeatureVector(finalStats);
      console.log('Feature vector:', features);

      // Get prediction using simple rules
      const prediction = this.classifyStyle(features);

      console.log('🤖 AI Prediction:', {
        predictedStyle: prediction.label,
        confidence: prediction.confidence,
        rawStats: finalStats,
        features: features
      });

      return {
        style: prediction.label,
        confidence: prediction.confidence
      };

    } catch (error) {
      console.error('Error predicting player style:', error);
      return null;
    }
  }

  // Method to add new training examples (simplified for now)
  async addTrainingExample(finalStats, actualStyle) {
    if (!this.isInitialized) return;
    console.log(`Would add training example for style: ${actualStyle}`);
  }

  getClassifierInfo() {
    return {
      mode: 'simple-rules',
      isInitialized: this.isInitialized
    };
  }
}

// Create singleton instance
export const aiDirector = new AIDirector();
