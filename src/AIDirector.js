import * as tf from '@tensorflow/tfjs';
import * as knnClassifier from '@tensorflow-models/knn-classifier';

class AIDirector {
  constructor() {
    this.styleClassifier = null;
    this.isInitialized = false;
    this.init();
  }

  async init() {
    try {
      console.log('Initializing AI Director...');
      
      // Create KNN classifier
      this.styleClassifier = knnClassifier.create();
      
      // Train the classifier with example play styles
      await this.trainStyleClassifier();
      
      this.isInitialized = true;
      console.log('AI Director initialized successfully!');
    } catch (error) {
      console.error('Failed to initialize AI Director:', error);
    }
  }

  async trainStyleClassifier() {
    // Define different play styles with example feature vectors
    
    // Aggressive style: High movement, high kills, low survival time, close to enemies
    const aggressiveExamples = [
      [0.8, 0.9, 0.3, 0.7, 0.6, 0.8, 0.4], // High kills, high movement, low danger time ratio
      [0.9, 0.8, 0.2, 0.8, 0.5, 0.9, 0.3],
      [0.7, 0.9, 0.4, 0.6, 0.7, 0.7, 0.5]
    ];

    // Defensive style: Lower movement, moderate kills, high survival, far from enemies
    const defensiveExamples = [
      [0.4, 0.3, 0.8, 0.3, 0.9, 0.4, 0.8], // Lower kills, low movement, high danger time ratio
      [0.3, 0.4, 0.9, 0.2, 0.8, 0.3, 0.9],
      [0.5, 0.2, 0.7, 0.4, 0.9, 0.5, 0.7]
    ];

    // Balanced style: Moderate values across all metrics
    const balancedExamples = [
      [0.6, 0.6, 0.6, 0.5, 0.6, 0.6, 0.6],
      [0.5, 0.7, 0.5, 0.6, 0.5, 0.7, 0.5],
      [0.7, 0.5, 0.7, 0.5, 0.7, 0.5, 0.7]
    ];

    // Add examples to classifier
    for (const example of aggressiveExamples) {
      const tensor = tf.tensor2d([example]);
      this.styleClassifier.addExample(tensor, 'aggressive');
      tensor.dispose();
    }

    for (const example of defensiveExamples) {
      const tensor = tf.tensor2d([example]);
      this.styleClassifier.addExample(tensor, 'defensive');
      tensor.dispose();
    }

    for (const example of balancedExamples) {
      const tensor = tf.tensor2d([example]);
      this.styleClassifier.addExample(tensor, 'balanced');
      tensor.dispose();
    }

    console.log('Style classifier trained with example data');
  }

  normalizeStats(stats) {
    // Normalize stats to 0-1 range for better ML performance
    const normalized = {
      killsPerSecond: Math.min(stats.killsPerSecond / 2, 1), // Normalize assuming max 2 kills/sec
      movementPerSecond: Math.min(stats.movementPerSecond / 200, 1), // Normalize assuming max 200 pixels/sec
      dangerTimeRatio: Math.min(stats.dangerTimeRatio, 1),
      survivalEfficiency: Math.min(stats.survivalEfficiency / 10, 1), // Normalize assuming max 10:1 ratio
      averageDistanceToEnemies: Math.min(stats.averageDistanceToEnemies / 300, 1), // Normalize assuming max 300 pixels
      enemiesKilled: Math.min(stats.enemiesKilled / 20, 1), // Normalize assuming max 20 kills per wave
      maxEnemiesAlive: Math.min(stats.maxEnemiesAlive / 10, 1) // Normalize assuming max 10 enemies
    };

    return normalized;
  }

  createFeatureVector(stats) {
    const normalized = this.normalizeStats(stats);
    
    // Create feature vector: [kills/sec, movement/sec, danger_ratio, survival_eff, avg_distance, total_kills, max_enemies]
    return [
      normalized.killsPerSecond,
      normalized.movementPerSecond,
      normalized.dangerTimeRatio,
      normalized.survivalEfficiency,
      normalized.averageDistanceToEnemies,
      normalized.enemiesKilled,
      normalized.maxEnemiesAlive
    ];
  }

  async predictPlayerStyle(finalStats) {
    if (!this.isInitialized || !this.styleClassifier) {
      console.warn('AI Director not initialized yet');
      return null;
    }

    try {
      // Create feature vector from stats
      const features = this.createFeatureVector(finalStats);
      console.log('Feature vector:', features);

      // Convert to tensor
      const tensor = tf.tensor2d([features]);

      // Get prediction
      const prediction = await this.styleClassifier.predictClass(tensor);
      
      // Clean up tensor
      tensor.dispose();

      console.log('🤖 AI Prediction:', {
        predictedStyle: prediction.label,
        confidence: prediction.confidences,
        rawStats: finalStats,
        features: features
      });

      return {
        style: prediction.label,
        confidence: prediction.confidences[prediction.label],
        allConfidences: prediction.confidences
      };

    } catch (error) {
      console.error('Error predicting player style:', error);
      return null;
    }
  }

  // Method to add new training examples based on actual gameplay
  async addTrainingExample(finalStats, actualStyle) {
    if (!this.isInitialized) return;

    try {
      const features = this.createFeatureVector(finalStats);
      const tensor = tf.tensor2d([features]);
      this.styleClassifier.addExample(tensor, actualStyle);
      tensor.dispose();
      
      console.log(`Added training example for style: ${actualStyle}`);
    } catch (error) {
      console.error('Error adding training example:', error);
    }
  }

  getClassifierInfo() {
    if (!this.styleClassifier) return null;
    
    return {
      numClasses: this.styleClassifier.getNumClasses(),
      classDatasetSize: this.styleClassifier.getClassDatasetSize(),
      isInitialized: this.isInitialized
    };
  }
}

// Create singleton instance
export const aiDirector = new AIDirector();
