import Phaser from 'phaser';
import Player from './src/Player.js';
import Enemy from './src/Enemy.js';
import { statsTracker } from './src/StatsTracker.js';
import { aiDirector } from './src/AIDirector.js';

// Main game scene
class GameScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameScene' });

    // Wave system
    this.currentWave = 1;
    this.enemiesInWave = 10;
    this.enemiesSpawned = 0;
    this.waveInProgress = false;
  }

  preload() {
    // Create simple colored rectangles as sprites
    this.createPlayerSprite();
    this.createEnemySprite();
  }

  createPlayerSprite() {
    // Create a blue rectangle for the player
    const graphics = this.add.graphics();
    graphics.fillStyle(0x0066ff);
    graphics.fillRect(0, 0, 32, 32);
    graphics.generateTexture('player', 32, 32);
    graphics.destroy();
  }

  createEnemySprite() {
    // Create a red rectangle for enemies
    const graphics = this.add.graphics();
    graphics.fillStyle(0xff3333);
    graphics.fillRect(0, 0, 24, 24);
    graphics.generateTexture('enemy', 24, 24);
    graphics.destroy();
  }

  create() {
    // Create player
    this.player = new Player(this, 400, 300);

    // Set up keyboard controls
    this.cursors = this.input.keyboard.createCursorKeys();

    // Create enemy group
    this.enemies = this.physics.add.group({
      classType: Enemy,
      runChildUpdate: true // This ensures enemy update methods are called
    });

    // Create garlic aura (invisible zone around player for combat)
    this.garlicAura = this.add.zone(this.player.x, this.player.y, 80, 80);
    this.physics.add.existing(this.garlicAura);
    this.garlicAura.body.setCircle(40);

    // Set up collisions and overlaps
    this.setupCollisions();

    // Start first wave
    this.startWave();

    // UI
    this.add.text(10, 10, 'Use arrow keys to move', { fontSize: '16px', fill: '#ffffff' });
    this.add.text(10, 70, 'Enemies take damage in your aura', { fontSize: '14px', fill: '#ffff00' });
    this.waveText = this.add.text(10, 100, `Wave: ${this.currentWave}`, { fontSize: '16px', fill: '#00ff00' });
    this.enemiesText = this.add.text(10, 120, `Enemies: ${this.enemies.children.size}`, { fontSize: '14px', fill: '#ffffff' });
  }

  setupCollisions() {
    // Player-Enemy collision (enemies damage player)
    this.physics.add.overlap(this.player, this.enemies, this.handlePlayerEnemyCollision, null, this);

    // Garlic aura-Enemy overlap (aura damages enemies)
    this.physics.add.overlap(this.garlicAura, this.enemies, this.handleGarlicEnemyOverlap, null, this);
  }

  startWave() {
    this.waveInProgress = true;
    this.enemiesSpawned = 0;

    // Start tracking this wave
    statsTracker.startNewWave(this.currentWave);

    // Update UI
    if (this.waveText) {
      this.waveText.setText(`Wave: ${this.currentWave}`);
    }

    // Spawn enemies for this wave
    this.time.addEvent({
      delay: 1000, // Spawn every 1 second
      callback: this.spawnEnemyForWave,
      callbackScope: this,
      repeat: this.enemiesInWave - 1 // Spawn exactly this many enemies
    });
  }

  spawnEnemyForWave() {
    if (this.enemiesSpawned >= this.enemiesInWave) return;

    // Choose a random edge of the screen to spawn from
    const side = Phaser.Math.Between(0, 3);
    let x, y;

    switch (side) {
      case 0: // Top
        x = Phaser.Math.Between(0, 800);
        y = -50;
        break;
      case 1: // Right
        x = 850;
        y = Phaser.Math.Between(0, 600);
        break;
      case 2: // Bottom
        x = Phaser.Math.Between(0, 800);
        y = 650;
        break;
      case 3: // Left
        x = -50;
        y = Phaser.Math.Between(0, 600);
        break;
    }

    // Create enemy and add to group
    const enemy = new Enemy(this, x, y);
    this.enemies.add(enemy);
    this.enemiesSpawned++;

    // Track enemy spawn
    statsTracker.onEnemySpawned();
  }

  checkWaveCompletion() {
    // Wave is complete when all enemies are spawned and all are killed
    if (this.waveInProgress &&
        this.enemiesSpawned >= this.enemiesInWave &&
        this.enemies.children.size === 0) {
      this.completeWave();
    }
  }

  async completeWave() {
    this.waveInProgress = false;

    // Finalize wave stats and get AI prediction
    const finalStats = statsTracker.finalizeWaveStats();
    const prediction = await aiDirector.predictPlayerStyle(finalStats);

    // Show wave completion message
    const waveCompleteText = this.add.text(400, 250, `Wave ${this.currentWave} Complete!`, {
      fontSize: '24px',
      fill: '#00ff00'
    }).setOrigin(0.5);

    if (prediction) {
      const styleText = this.add.text(400, 280, `Play Style: ${prediction.style.toUpperCase()}`, {
        fontSize: '18px',
        fill: '#ffff00'
      }).setOrigin(0.5);

      // Remove texts after delay
      this.time.delayedCall(3000, () => {
        waveCompleteText.destroy();
        styleText.destroy();
      });
    } else {
      this.time.delayedCall(3000, () => {
        waveCompleteText.destroy();
      });
    }

    // Start next wave after delay
    this.time.delayedCall(4000, () => {
      this.currentWave++;
      this.enemiesInWave += 2; // Increase difficulty
      this.startWave();
    });
  }

  handlePlayerEnemyCollision(player, enemy) {
    // Enemy damages player if enough time has passed
    if (enemy.canDamagePlayer()) {
      player.takeDamage(enemy.damage);
      // Track damage taken
      statsTracker.onPlayerTakesDamage(enemy.damage);
    }
  }

  handleGarlicEnemyOverlap(aura, enemy) {
    // Garlic aura damages enemies over time
    const damage = 1;
    enemy.takeDamage(damage);

    // Track if enemy was killed
    if (enemy.health <= 0) {
      statsTracker.onEnemyKilled(damage);
      // Check if wave is complete
      this.time.delayedCall(100, () => this.checkWaveCompletion());
    }
  }

  update() {
    // Update player
    if (this.player && this.player.active) {
      this.player.update(this.cursors);

      // Update garlic aura position to follow player
      this.garlicAura.x = this.player.x;
      this.garlicAura.y = this.player.y;

      // Track player position and enemy proximity
      statsTracker.updatePlayerPosition(this.player.x, this.player.y);

      // Get active enemies for proximity tracking
      const activeEnemies = this.enemies.children.entries.filter(enemy => enemy.active);
      statsTracker.updateEnemyProximity(activeEnemies, this.player.x, this.player.y);

      // Update UI
      if (this.enemiesText) {
        this.enemiesText.setText(`Enemies: ${activeEnemies.length}`);
      }
    }
  }
}

// Phaser game configuration
const config = {
  type: Phaser.AUTO, // Automatically use WebGL or Canvas
  width: 800,
  height: 600,
  parent: 'game-container',
  physics: {
    default: 'arcade',
    arcade: {
      gravity: { y: 0 },
    }
  },
  scene: [GameScene]
};

// Create the game instance
const game = new Phaser.Game(config);
